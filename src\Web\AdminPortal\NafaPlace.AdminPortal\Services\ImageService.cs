using System;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.JSInterop;

namespace NafaPlace.AdminPortal.Services
{
    public class ImageService
    {
        private readonly HttpClient _httpClient;
        private readonly IJSRuntime _jsRuntime;
        private readonly string _apiBaseUrl = "http://localhost:5000"; // URL de la Gateway
        private int _lastUploadedImageId = 0;

        public ImageService(HttpClient httpClient, IJSRuntime jsRuntime)
        {
            _httpClient = httpClient;
            _httpClient.BaseAddress = new Uri(_apiBaseUrl);
            _jsRuntime = jsRuntime;
        }

        public async Task<string> ConvertFileToBase64(IBrowserFile file)
        {
            try
            {
                // Limiter la taille du fichier à 5 Mo
                var maxFileSize = 5 * 1024 * 1024;
                
                // Utiliser MemoryStream pour éviter les lectures incorrectes
                using var ms = new MemoryStream();
                await using var stream = file.OpenReadStream(maxFileSize);
                
                // Copier le contenu du stream dans le MemoryStream
                await stream.CopyToAsync(ms);
                
                // Convertir en base64
                return Convert.ToBase64String(ms.ToArray());
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        public async Task<string> UploadProductImageAsync(int productId, string base64Image, bool isMain = false)
        {
            try
            {
                // Vérifier si l'image en base64 est valide
                if (string.IsNullOrEmpty(base64Image))
                {
                    return string.Empty;
                }

                // Supprimer le préfixe data:image si présent
                string cleanBase64 = base64Image;
                if (base64Image.Contains(","))
                {
                    cleanBase64 = base64Image.Split(',')[1];
                }

                // Créer le contenu JSON comme le SellerPortal (objet simple)
                var requestObject = new
                {
                    Image = cleanBase64,
                    IsMain = isMain
                };

                var content = new StringContent(
                    JsonSerializer.Serialize(requestObject),
                    Encoding.UTF8,
                    "application/json");

                // Utiliser la même route que le SellerPortal (via gateway)
                var response = await _httpClient.PostAsync($"/api/catalog/products/{productId}/images", content);
                
                if (response.IsSuccessStatusCode)
                {
                    var jsonString = await response.Content.ReadAsStringAsync();

                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };

                    // L'API bulk retourne un tableau d'images
                    var results = JsonSerializer.Deserialize<ProductImageUploadResult[]>(jsonString, options);
                    var result = results?.FirstOrDefault();

                    // Stocker l'ID de l'image pour une utilisation ultérieure
                    _lastUploadedImageId = result?.Id ?? 0;

                    // Retourner l'URL de l'image ou une URL de données base64 pour l'affichage immédiat
                    if (result != null && !string.IsNullOrEmpty(result.ThumbnailUrl))
                    {
                        return result.ThumbnailUrl;
                    }

                    // Fallback vers une URL de données base64 pour l'affichage immédiat
                    if (!string.IsNullOrEmpty(cleanBase64))
                    {
                        return $"data:image/jpeg;base64,{cleanBase64}";
                    }

                    return result?.ImageUrl ?? string.Empty;
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Erreur API: {error}");
                    return string.Empty;
                }
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        public async Task<bool> DeleteProductImageAsync(int productId, int imageId)
        {
            try
            {
                // Utiliser la route via la gateway (comme configuré dans ocelot.json)
                var response = await _httpClient.DeleteAsync($"/api/catalog/products/{productId}/images/{imageId}");
                
                return response.IsSuccessStatusCode;
            }
            catch (Exception)
            {
                return false;
            }
        }
        
        public async Task<bool> SetMainImageAsync(int productId, int imageId)
        {
            try
            {
                // Utiliser la route via la gateway (comme configuré dans ocelot.json)
                var response = await _httpClient.PutAsync($"/api/catalog/products/{productId}/images/{imageId}/main", null);
                
                return response.IsSuccessStatusCode;
            }
            catch (Exception)
            {
                return false;
            }
        }
        
        public Task<int> GetLastUploadedImageId()
        {
            return Task.FromResult(_lastUploadedImageId);
        }
        
        // Méthode générique pour uploader une image
        public async Task<ImageUploadResult> UploadImageAsync(IBrowserFile file, string category = "general")
        {
            try
            {
                // Convertir le fichier en base64
                var base64Image = await ConvertFileToBase64(file);

                if (string.IsNullOrEmpty(base64Image))
                {
                    return new ImageUploadResult { Success = false, ErrorMessage = "Impossible de convertir le fichier en base64" };
                }

                // Utiliser la méthode appropriée selon la catégorie
                string imageUrl = category.ToLower() switch
                {
                    "categories" => await UploadCategoryImageAsync(base64Image),
                    _ => await UploadCategoryImageAsync(base64Image) // Par défaut, utiliser la méthode de catégorie
                };

                if (!string.IsNullOrEmpty(imageUrl))
                {
                    return new ImageUploadResult { Success = true, ImageUrl = imageUrl };
                }
                else
                {
                    return new ImageUploadResult { Success = false, ErrorMessage = "Échec de l'upload de l'image" };
                }
            }
            catch (Exception ex)
            {
                return new ImageUploadResult { Success = false, ErrorMessage = ex.Message };
            }
        }

        // Méthode pour uploader une image de catégorie
        public async Task<string> UploadCategoryImageAsync(string base64Image)
        {
            try
            {
                // Vérifier si l'image en base64 est valide
                if (string.IsNullOrEmpty(base64Image))
                {
                    return string.Empty;
                }

                // Supprimer le préfixe data:image si présent
                string cleanBase64 = base64Image;
                if (base64Image.Contains(","))
                {
                    cleanBase64 = base64Image.Split(',')[1];
                }

                // Créer le contenu JSON pour l'API
                var imageRequest = new
                {
                    Image = cleanBase64
                };

                var content = new StringContent(
                    JsonSerializer.Serialize(imageRequest),
                    Encoding.UTF8,
                    "application/json");

                // Envoyer la requête à l'API
                var response = await _httpClient.PostAsync("/api/v1/categories/upload-image", content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<CategoryImageUploadResult>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return result?.ImageUrl ?? string.Empty;
                }

                return string.Empty;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de l'upload de l'image de catégorie: {ex.Message}");
                return string.Empty;
            }
        }

        // Classe pour désérialiser la réponse de l'API bulk
        private class ProductImageUploadResult
        {
            public int Id { get; set; }
            public string ImageUrl { get; set; } = string.Empty;
            public string ThumbnailUrl { get; set; } = string.Empty;
            public bool IsMain { get; set; }
            public int ProductId { get; set; }
        }

        // Classe pour désérialiser la réponse de l'upload d'image de catégorie
        private class CategoryImageUploadResult
        {
            public string ImageUrl { get; set; } = string.Empty;
        }
    }

    // Classe publique pour les résultats d'upload d'images
    public class ImageUploadResult
    {
        public bool Success { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
    }
}
