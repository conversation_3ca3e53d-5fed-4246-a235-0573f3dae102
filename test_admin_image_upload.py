#!/usr/bin/env python3
"""
Script de test pour vérifier l'ajout d'images dans l'AdminPortal
Ce script simule exactement ce que fait l'AdminPortal pour ajouter une image
"""

import requests
import base64
import json
import sys
from pathlib import Path

def create_test_image_base64():
    """Crée une petite image de test en base64"""
    # Image PNG 1x1 pixel transparent (plus petite possible)
    png_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\nIDATx\x9cc\x00\x01\x00\x00\x05\x00\x01\r\n-\xdb\x00\x00\x00\x00IEND\xaeB`\x82'
    return base64.b64encode(png_data).decode('utf-8')

def test_admin_image_upload():
    """Test l'ajout d'image via l'API comme le fait l'AdminPortal"""
    
    print("🧪 Test d'ajout d'image AdminPortal")
    print("=" * 50)
    
    # Configuration (même que l'AdminPortal)
    api_base_url = "http://localhost:5243"  # Accès direct à l'API Catalog
    product_id = 63  # ID du produit de test
    
    # Créer une image de test
    test_image_base64 = create_test_image_base64()
    
    # Préparer la requête (exactement comme l'AdminPortal)
    request_data = {
        "Image": test_image_base64,
        "IsMain": False
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    url = f"{api_base_url}/api/v1/products/{product_id}/images"
    
    print(f"📡 URL: {url}")
    print(f"📦 Taille de l'image: {len(test_image_base64)} caractères")
    print(f"🔧 Headers: {headers}")
    print(f"📄 Payload: {{'Image': '[{len(test_image_base64)} chars]', 'IsMain': {request_data['IsMain']}}}")
    
    try:
        print("\n⏳ Envoi de la requête...")
        response = requests.post(url, json=request_data, headers=headers, timeout=30)
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📋 Headers de réponse: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ SUCCESS: L'ajout d'image fonctionne!")
            try:
                response_data = response.json()
                print(f"📄 Réponse: {json.dumps(response_data, indent=2)}")
            except:
                print(f"📄 Réponse (texte): {response.text}")
        else:
            print(f"❌ ERREUR: Status {response.status_code}")
            print(f"📄 Réponse: {response.text}")
            
            # Vérifier si c'est une erreur de base de données
            if "value too long" in response.text.lower():
                print("🔍 Diagnostic: Erreur de taille en base de données")
            elif "404" in str(response.status_code):
                print("🔍 Diagnostic: Route API non trouvée")
            elif "400" in str(response.status_code):
                print("🔍 Diagnostic: Erreur de validation des données")
                
    except requests.exceptions.ConnectionError:
        print("❌ ERREUR: Impossible de se connecter à l'API")
        print("🔍 Vérifiez que l'API Catalog est démarrée sur le port 5243")
    except requests.exceptions.Timeout:
        print("❌ ERREUR: Timeout de la requête")
    except Exception as e:
        print(f"❌ ERREUR inattendue: {e}")

def test_api_health():
    """Teste si l'API Catalog est accessible"""
    print("\n🏥 Test de santé de l'API Catalog")
    print("=" * 40)
    
    try:
        response = requests.get("http://localhost:5243/health", timeout=5)
        print(f"✅ API Catalog accessible: {response.status_code}")
    except:
        try:
            # Essayer une route basique
            response = requests.get("http://localhost:5243/api/v1/products", timeout=5)
            print(f"✅ API Catalog accessible via /products: {response.status_code}")
        except Exception as e:
            print(f"❌ API Catalog non accessible: {e}")

if __name__ == "__main__":
    test_api_health()
    test_admin_image_upload()
    
    print("\n" + "=" * 50)
    print("🎯 Résumé:")
    print("- Si SUCCESS: L'AdminPortal devrait fonctionner")
    print("- Si ERREUR: Il faut corriger le problème identifié")
    print("=" * 50)
