FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

COPY ["src/Web/SellerPortal/NafaPlace.SellerPortal/NafaPlace.SellerPortal.csproj", "src/Web/SellerPortal/NafaPlace.SellerPortal/"]
COPY ["src/BuildingBlocks/Common/NafaPlace.Common/NafaPlace.Common.csproj", "src/BuildingBlocks/Common/NafaPlace.Common/"]
RUN dotnet restore "src/Web/SellerPortal/NafaPlace.SellerPortal/NafaPlace.SellerPortal.csproj"

COPY . .
WORKDIR "/src/src/Web/SellerPortal/NafaPlace.SellerPortal"
RUN dotnet build "NafaPlace.SellerPortal.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "NafaPlace.SellerPortal.csproj" -c Release -o /app/publish

FROM nginx:alpine AS final
WORKDIR /usr/share/nginx/html
COPY --from=publish /app/publish/wwwroot .

# Use local development nginx configuration
COPY src/Web/SellerPortal/nginx.local.conf /etc/nginx/nginx.conf

# Copy development configuration to override production settings
COPY src/Web/SellerPortal/NafaPlace.SellerPortal/wwwroot/appsettings.Development.json /usr/share/nginx/html/appsettings.json

# Configuration pour le marché africain
ENV TZ=Africa/Conakry
ENV LANG=fr_GN.UTF-8
ENV LANGUAGE=fr_GN.UTF-8
ENV LC_ALL=fr_GN.UTF-8

# Railway uses PORT environment variable
ENV PORT=8080
EXPOSE $PORT

# Set permissions for existing nginx user and create necessary directories
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    mkdir -p /var/run/nginx && \
    chown -R nginx:nginx /var/run/nginx

# Switch to nginx user
USER nginx

# Start nginx with envsubst to replace $PORT in config
CMD ["sh", "-c", "envsubst '$PORT' < /etc/nginx/nginx.conf > /tmp/nginx.conf && exec nginx -c /tmp/nginx.conf -g 'daemon off;'"]
